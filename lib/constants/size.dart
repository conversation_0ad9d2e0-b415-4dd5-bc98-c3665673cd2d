import 'package:flutter/material.dart';

const double defaultPixel = 8;

extension SizedBoxPadding on num {
  SizedBox get ph => SizedBox(height: defaultPixel * toDouble());

  SizedBox get pw => SizedBox(width: defaultPixel * toDouble());

  double get m8 => defaultPixel * toDouble();
}

class MySize {
  static late MediaQueryData _mediaQueryData;
  static late double screenWidth;
  static late double screenHeight;
  static double? safeWidth;
  static double? safeHeight;

  static late double scaleFactorWidth;
  static late double scaleFactorHeight;

  // 0-99
  static late double size0;
  static late double size1;
  static late double size2;
  static late double size3;
  static late double size4;
  static late double size5;
  static late double size6;
  static late double size7;
  static late double size8;
  static late double size9;
  static late double size10;
  static late double size11;
  static late double size12;
  static late double size13;
  static late double size14;
  static late double size15;
  static late double size16;
  static late double size17;
  static late double size18;
  static late double size19;
  static late double size20;
  static late double size21;
  static late double size22;
  static late double size23;
  static late double size24;
  static late double size25;
  static late double size26;
  static late double size27;
  static late double size28;
  static late double size29;
  static late double size30;
  static late double size31;
  static late double size32;
  static late double size33;
  static late double size34;
  static late double size35;
  static late double size36;
  static late double size37;
  static late double size38;
  static late double size39;
  static late double size40;
  static late double size41;
  static late double size42;
  static late double size43;
  static late double size44;
  static late double size45;
  static late double size46;
  static late double size47;
  static late double size48;
  static late double size49;
  static late double size50;
  static late double size51;
  static late double size52;
  static late double size53;
  static late double size54;
  static late double size55;
  static late double size56;
  static late double size57;
  static late double size58;
  static late double size59;
  static late double size60;
  static late double size61;
  static late double size62;
  static late double size63;
  static late double size64;
  static late double size65;
  static late double size66;
  static late double size67;
  static late double size68;
  static late double size69;
  static late double size70;
  static late double size71;
  static late double size72;
  static late double size73;
  static late double size74;
  static late double size75;
  static late double size76;
  static late double size77;
  static late double size78;
  static late double size79;
  static late double size80;
  static late double size81;
  static late double size82;
  static late double size83;
  static late double size84;
  static late double size85;
  static late double size86;
  static late double size87;
  static late double size88;
  static late double size89;
  static late double size90;
  static late double size91;
  static late double size92;
  static late double size93;
  static late double size94;
  static late double size95;
  static late double size96;
  static late double size97;
  static late double size98;
  static late double size99;
  static late double size100;

// 100-199
  static late double size101;
  static late double size102;
  static late double size103;
  static late double size104;
  static late double size105;
  static late double size108;
  static late double size110;
  static late double size112;
  static late double size115;
  static late double size117;
  static late double size118;
  static late double size120;
  static late double size121;
  static late double size122;
  static late double size123;
  static late double size125;
  static late double size126;
  static late double size127;
  static late double size128;
  static late double size129;
  static late double size131;
  static late double size135;
  static late double size136;
  static late double size137;
  static late double size140;
  static late double size142;
  static late double size144;
  static late double size146;
  static late double size147;
  static late double size148;
  static late double size150;
  static late double size151;
  static late double size153;
  static late double size155;
  static late double size156;
  static late double size158;
  static late double size160;
  static late double size162;
  static late double size164;
  static late double size167;
  static late double size168;
  static late double size170;
  static late double size172;
  static late double size174;
  static late double size175;
  static late double size176;
  static late double size178;
  static late double size180;
  static late double size182;
  static late double size186;
  static late double size187;
  static late double size188;
  static late double size191;
  static late double size198;

// 200-299
  static late double size200;
  static late double size205;
  static late double size207;
  static late double size208;
  static late double size210;
  static late double size212;
  static late double size215;
  static late double size216;
  static late double size218;
  static late double size220;
  static late double size221;
  static late double size224;
  static late double size225;
  static late double size230;
  static late double size232;
  static late double size237;
  static late double size240;
  static late double size242;
  static late double size243;
  static late double size248;
  static late double size249;
  static late double size250;
  static late double size253;
  static late double size254;
  static late double size255;
  static late double size258;
  static late double size266;
  static late double size268;
  static late double size270;
  static late double size278;
  static late double size280;
  static late double size282;
  static late double size285;
  static late double size287;
  static late double size290;
  static late double size293;
  static late double size294;
  static late double size295;
  static late double size296;

// 300-399
  static late double size300;
  static late double size303;
  static late double size304;
  static late double size310;
  static late double size315;
  static late double size316;
  static late double size317;
  static late double size318;
  static late double size319;
  static late double size320;
  static late double size325;
  static late double size326;
  static late double size327;
  static late double size329;
  static late double size330;
  static late double size335;
  static late double size340;
  static late double size350;
  static late double size351;
  static late double size358;
  static late double size360;
  static late double size368;
  static late double size370;
  static late double size397;

// 400+
  static late double size400;
  static late double size404;
  static late double size405;
  static late double size410;
  static late double size419;
  static late double size420;
  static late double size421;
  static late double size425;
  static late double size437;
  static late double size447;
  static late double size455;
  static late double size470;
  static late double size480;
  static late double size500;
  static late double size550;
  static late double size553;
  static late double size560;
  static late double size582;
  static late double size585;
  static late double size590;
  static late double size595;
  static late double size600;
  static late double size635;
  static late double size690;
  static late double size700;
  static late double size730;
  static late double size1081;

  void init(BuildContext context) {
    _mediaQueryData = MediaQuery.of(context);

    screenWidth = _mediaQueryData.size.width;
    screenHeight = _mediaQueryData.size.height;

    double safeAreaWidth =
        _mediaQueryData.padding.left + _mediaQueryData.padding.right;
    double safeAreaHeight =
        _mediaQueryData.padding.top + _mediaQueryData.padding.bottom;
    safeWidth = (screenWidth - safeAreaWidth);
    safeHeight = (screenHeight - safeAreaHeight);

    safeWidth = (screenWidth - safeAreaWidth);
    safeHeight = (screenHeight - safeAreaHeight);

    scaleFactorHeight = (safeHeight! / 896);
    if (scaleFactorHeight < 1) {
      double diff = (1 - scaleFactorHeight) * (1 - scaleFactorHeight);
      scaleFactorHeight += diff;
    }

    scaleFactorWidth = (safeWidth! / 414);

    if (scaleFactorWidth < 1) {
      double diff = (1 - scaleFactorWidth) * (1 - scaleFactorWidth);
      scaleFactorWidth += diff;
    }

    size0 = scaleFactorHeight * 0;
    size1 = scaleFactorHeight * 1;
    size2 = scaleFactorHeight * 2;
    size3 = scaleFactorHeight * 3;
    size4 = scaleFactorHeight * 4;
    size5 = scaleFactorHeight * 5;
    size6 = scaleFactorHeight * 6;
    size7 = scaleFactorHeight * 7;
    size8 = scaleFactorHeight * 8;
    size9 = scaleFactorHeight * 9;
    size10 = scaleFactorHeight * 10;
    size11 = scaleFactorHeight * 11;
    size12 = scaleFactorHeight * 12;
    size13 = scaleFactorHeight * 13;
    size14 = scaleFactorHeight * 14;
    size15 = scaleFactorHeight * 15;
    size16 = scaleFactorHeight * 16;
    size17 = scaleFactorHeight * 17;
    size18 = scaleFactorHeight * 18;
    size19 = scaleFactorHeight * 19;
    size20 = scaleFactorHeight * 20;
    size21 = scaleFactorHeight * 21;
    size22 = scaleFactorHeight * 22;
    size23 = scaleFactorHeight * 23;
    size24 = scaleFactorHeight * 24;
    size25 = scaleFactorHeight * 25;
    size26 = scaleFactorHeight * 26;
    size27 = scaleFactorHeight * 27;
    size28 = scaleFactorHeight * 28;
    size29 = scaleFactorHeight * 29;
    size30 = scaleFactorHeight * 30;
    size31 = scaleFactorHeight * 31;
    size32 = scaleFactorHeight * 32;
    size33 = scaleFactorHeight * 33;
    size34 = scaleFactorHeight * 34;
    size35 = scaleFactorHeight * 35;
    size36 = scaleFactorHeight * 36;
    size37 = scaleFactorHeight * 37;
    size38 = scaleFactorHeight * 38;
    size39 = scaleFactorHeight * 39;
    size40 = scaleFactorHeight * 40;
    size41 = scaleFactorHeight * 41;
    size42 = scaleFactorHeight * 42;
    size43 = scaleFactorHeight * 43;
    size44 = scaleFactorHeight * 44;
    size45 = scaleFactorHeight * 45;
    size46 = scaleFactorHeight * 46;
    size47 = scaleFactorHeight * 47;
    size48 = scaleFactorHeight * 48;
    size49 = scaleFactorHeight * 49;
    size50 = scaleFactorHeight * 50;
    size51 = scaleFactorHeight * 51;
    size52 = scaleFactorHeight * 52;
    size53 = scaleFactorHeight * 53;
    size54 = scaleFactorHeight * 54;
    size55 = scaleFactorHeight * 55;
    size56 = scaleFactorHeight * 56;
    size57 = scaleFactorHeight * 57;
    size58 = scaleFactorHeight * 58;
    size59 = scaleFactorHeight * 59;
    size60 = scaleFactorHeight * 60;
    size61 = scaleFactorHeight * 61;
    size62 = scaleFactorHeight * 62;
    size63 = scaleFactorHeight * 63;
    size64 = scaleFactorHeight * 64;
    size65 = scaleFactorHeight * 65;
    size66 = scaleFactorHeight * 66;
    size67 = scaleFactorHeight * 67;
    size68 = scaleFactorHeight * 68;
    size69 = scaleFactorHeight * 69;
    size70 = scaleFactorHeight * 70;
    size71 = scaleFactorHeight * 71;
    size72 = scaleFactorHeight * 72;
    size73 = scaleFactorHeight * 73;
    size74 = scaleFactorHeight * 74;
    size75 = scaleFactorHeight * 75;
    size76 = scaleFactorHeight * 76;
    size77 = scaleFactorHeight * 77;
    size78 = scaleFactorHeight * 78;
    size79 = scaleFactorHeight * 79;
    size80 = scaleFactorHeight * 80;
    size81 = scaleFactorHeight * 81;
    size82 = scaleFactorHeight * 82;
    size83 = scaleFactorHeight * 83;
    size84 = scaleFactorHeight * 84;
    size85 = scaleFactorHeight * 85;
    size86 = scaleFactorHeight * 86;
    size87 = scaleFactorHeight * 87;
    size88 = scaleFactorHeight * 88;
    size89 = scaleFactorHeight * 89;
    size90 = scaleFactorHeight * 90;
    size91 = scaleFactorHeight * 91;
    size92 = scaleFactorHeight * 92;
    size93 = scaleFactorHeight * 93;
    size94 = scaleFactorHeight * 94;
    size95 = scaleFactorHeight * 95;
    size96 = scaleFactorHeight * 96;
    size97 = scaleFactorHeight * 97;
    size98 = scaleFactorHeight * 98;
    size99 = scaleFactorHeight * 99;
    size100 = scaleFactorHeight * 100;
    size102 = scaleFactorHeight * 102;
    size103 = scaleFactorHeight * 103;
    size104 = scaleFactorHeight * 104;
    size105 = scaleFactorHeight * 105;
    size108 = scaleFactorHeight * 108;
    size110 = scaleFactorHeight * 110;
    size112 = scaleFactorHeight * 112;
    size115 = scaleFactorHeight * 115;
    size117 = scaleFactorHeight * 117;
    size118 = scaleFactorHeight * 118;
    size120 = scaleFactorHeight * 120;
    size121 = scaleFactorHeight * 121;
    size122 = scaleFactorHeight * 122;
    size123 = scaleFactorHeight * 123;
    size125 = scaleFactorHeight * 125;
    size126 = scaleFactorHeight * 126;
    size127 = scaleFactorHeight * 127;
    size128 = scaleFactorHeight * 128;
    size129 = scaleFactorHeight * 129;
    size131 = scaleFactorHeight * 131;
    size135 = scaleFactorHeight * 135;
    size136 = scaleFactorHeight * 136;
    size137 = scaleFactorHeight * 137;
    size140 = scaleFactorHeight * 140;
    size142 = scaleFactorHeight * 142;
    size144 = scaleFactorHeight * 144;
    size146 = scaleFactorHeight * 146;
    size147 = scaleFactorHeight * 147;
    size148 = scaleFactorHeight * 148;
    size150 = scaleFactorHeight * 150;
    size151 = scaleFactorHeight * 151;
    size153 = scaleFactorHeight * 153;
    size155 = scaleFactorHeight * 155;
    size156 = scaleFactorHeight * 156;
    size158 = scaleFactorHeight * 158;
    size160 = scaleFactorHeight * 160;
    size162 = scaleFactorHeight * 162;
    size164 = scaleFactorHeight * 164;
    size167 = scaleFactorHeight * 167;
    size168 = scaleFactorHeight * 168;
    size170 = scaleFactorHeight * 170;
    size172 = scaleFactorHeight * 172;
    size174 = scaleFactorHeight * 174;
    size175 = scaleFactorHeight * 175;
    size176 = scaleFactorHeight * 176;
    size178 = scaleFactorHeight * 178;
    size180 = scaleFactorHeight * 180;
    size182 = scaleFactorHeight * 182;
    size186 = scaleFactorHeight * 186;
    size187 = scaleFactorHeight * 187;
    size188 = scaleFactorHeight * 188;
    size191 = scaleFactorHeight * 191;
    size198 = scaleFactorHeight * 198;
    size200 = scaleFactorHeight * 200;
    size205 = scaleFactorHeight * 205;
    size207 = scaleFactorHeight * 207;
    size210 = scaleFactorHeight * 210;
    size212 = scaleFactorHeight * 212;
    size215 = scaleFactorHeight * 215;
    size216 = scaleFactorHeight * 216;
    size218 = scaleFactorHeight * 218;
    size220 = scaleFactorHeight * 220;
    size221 = scaleFactorHeight * 221;
    size224 = scaleFactorHeight * 224;
    size225 = scaleFactorHeight * 225;
    size230 = scaleFactorHeight * 230;
    size232 = scaleFactorHeight * 232;
    size237 = scaleFactorHeight * 237;
    size240 = scaleFactorHeight * 240;
    size242 = scaleFactorHeight * 242;
    size243 = scaleFactorHeight * 243;
    size248 = scaleFactorHeight * 248;
    size249 = scaleFactorHeight * 249;
    size250 = scaleFactorHeight * 250;
    size253 = scaleFactorHeight * 253;
    size254 = scaleFactorHeight * 254;
    size255 = scaleFactorHeight * 255;
    size258 = scaleFactorHeight * 258;
    size266 = scaleFactorHeight * 266;
    size268 = scaleFactorHeight * 268;
    size270 = scaleFactorHeight * 270;
    size278 = scaleFactorHeight * 278;
    size280 = scaleFactorHeight * 280;
    size282 = scaleFactorHeight * 282;
    size285 = scaleFactorHeight * 285;
    size290 = scaleFactorHeight * 290;
    size293 = scaleFactorHeight * 293;
    size294 = scaleFactorHeight * 294;
    size295 = scaleFactorHeight * 295;
    size296 = scaleFactorHeight * 296;
    size300 = scaleFactorHeight * 300;
    size303 = scaleFactorHeight * 303;
    size304 = scaleFactorHeight * 304;
    size310 = scaleFactorHeight * 310;
    size315 = scaleFactorHeight * 315;
    size316 = scaleFactorHeight * 316;
    size317 = scaleFactorHeight * 317;
    size318 = scaleFactorHeight * 318;
    size319 = scaleFactorHeight * 319;
    size320 = scaleFactorHeight * 320;
    size325 = scaleFactorHeight * 325;
    size326 = scaleFactorHeight * 326;
    size327 = scaleFactorHeight * 327;
    size330 = scaleFactorHeight * 330;
    size335 = scaleFactorHeight * 335;
    size340 = scaleFactorHeight * 340;
    size350 = scaleFactorHeight * 350;
    size351 = scaleFactorHeight * 351;
    size358 = scaleFactorHeight * 358;
    size360 = scaleFactorHeight * 360;
    size368 = scaleFactorHeight * 368;
    size370 = scaleFactorHeight * 370;
    size397 = scaleFactorHeight * 397;
    size400 = scaleFactorHeight * 400;
    size404 = scaleFactorHeight * 404;
    size405 = scaleFactorHeight * 405;
    size410 = scaleFactorHeight * 410;
    size419 = scaleFactorHeight * 419;
    size420 = scaleFactorHeight * 420;
    size421 = scaleFactorHeight * 421;
    size425 = scaleFactorHeight * 425;
    size437 = scaleFactorHeight * 437;
    size447 = scaleFactorHeight * 447;
    size455 = scaleFactorHeight * 455;
    size470 = scaleFactorHeight * 470;
    size480 = scaleFactorHeight * 480;
    size500 = scaleFactorHeight * 500;
    size550 = scaleFactorHeight * 550;
    size553 = scaleFactorHeight * 553;
    size582 = scaleFactorHeight * 582;
    size600 = scaleFactorHeight * 600;
    size635 = scaleFactorHeight * 635;
    size690 = scaleFactorHeight * 690;
    size730 = scaleFactorHeight * 730;
    size1081 = scaleFactorHeight * 1081;
  }

  static double getScaledSizeWidth(double size) {
    return (size * scaleFactorWidth);
  }

  static double getScaledSizeHeight(double size) {
    return (size * scaleFactorHeight);
  }
}

class SizeConfig {
  static MediaQueryData? _mediaQueryData;
  static double? screenWidth;
  static double? screenHeight;
  static double? blockSizeHorizontal;
  static double? blockSizeVertical;
  static double? _safeAreaHorizontal;
  static double? _safeAreaVertical;
  static double? safeBlockHorizontal;
  static double? safeBlockVertical;

  void init(BuildContext context) {
    _mediaQueryData = MediaQuery.of(context);
    screenWidth = _mediaQueryData!.size.width;
    screenHeight = _mediaQueryData!.size.height;
    blockSizeHorizontal = screenWidth! / 100;
    blockSizeVertical = screenHeight! / 100;
    _safeAreaHorizontal =
        _mediaQueryData!.padding.left + _mediaQueryData!.padding.right;
    _safeAreaVertical =
        _mediaQueryData!.padding.top + _mediaQueryData!.padding.bottom;
    safeBlockHorizontal = (screenWidth! - _safeAreaHorizontal!) / 100;
    safeBlockVertical = (screenHeight! - _safeAreaVertical!) / 100;
  }
}


class Space {
  Space();

  static Widget height(double space) {
    return SizedBox(
      height: MySize.getScaledSizeHeight(space),
    );
  }

  static Widget width(double space) {
    return SizedBox(
      width: MySize.getScaledSizeWidth(space),
    );
  }
}

enum ShapeTypeFor { container, button }

class Shape {
  static dynamic circular(double radius,
      {ShapeTypeFor shapeTypeFor = ShapeTypeFor.container}) {
    BorderRadius borderRadius =
        BorderRadius.all(Radius.circular(MySize.getScaledSizeHeight(radius)));

    switch (shapeTypeFor) {
      case ShapeTypeFor.container:
        return borderRadius;
      case ShapeTypeFor.button:
        return RoundedRectangleBorder(borderRadius: borderRadius);
    }
  }

  static dynamic circularTop(double radius,
      {ShapeTypeFor shapeTypeFor = ShapeTypeFor.container}) {
    BorderRadius borderRadius = BorderRadius.only(
        topLeft: Radius.circular(MySize.getScaledSizeHeight(radius)),
        topRight: Radius.circular(MySize.getScaledSizeHeight(radius)));
    switch (shapeTypeFor) {
      case ShapeTypeFor.container:
        return borderRadius;

      case ShapeTypeFor.button:
        return RoundedRectangleBorder(borderRadius: borderRadius);
    }
  }
}

bool isNullEmptyOrFalse(dynamic o) {
  if (o is Map<String, dynamic> || o is List<dynamic>) {
    return o == null || o.length == 0;
  }
  return o == null || false == o || "" == o;
}
