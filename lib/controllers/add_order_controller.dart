import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/add_order_model.dart';
import '../models/country_model.dart';

class AddOrderController extends GetxController {
  // Observable variables
  final RxInt selectedNavIndex = 2.obs; // Add Order is accessed from Orders List (index 2)
  final RxBool isLoading = false.obs;
  final RxString selectedPayment = 'Paid'.obs;
  final RxString selectedOrderStatus = 'Packing'.obs;
  final RxString productImagePath = ''.obs;
  final Rx<CountryModel> selectedCountry = CountryModel(
    name: 'India',
    code: 'IN',
    dialCode: '+91',
    flag: '🇮🇳',
  ).obs;

  // Form controllers
  final TextEditingController customerNameController = TextEditingController();
  final TextEditingController mobileNumberController = TextEditingController();
  final TextEditingController gstNumberController = TextEditingController();
  final TextEditingController emailIdController = TextEditingController();
  final TextEditingController businessNameController = TextEditingController();
  final TextEditingController businessAddressController = TextEditingController();
  final TextEditingController orderIdController = TextEditingController();
  final TextEditingController productNameController = TextEditingController();
  final TextEditingController priceController = TextEditingController();
  final TextEditingController quantityController = TextEditingController();
  final TextEditingController searchController = TextEditingController();

  // Dropdown options
  final List<String> paymentOptions = ['Paid', 'Pending', 'Partial'];
  final List<String> orderStatusOptions = [
    'Packing',
    'On-boarding',
    'Designing',
    'Sampling',
    'Design Plate Approved',
    'Cylinder Development',
    'Polyester Sample Approved',
    'Polyester Printing',
    'Lamination',
    'Metallised Pasting',
    'Heating',
    'Curing',
    'Zipper Addition',
    'Slitting',
    'Pouching',
    'Sorting',
    'Ready to Dispatch',
    'Dispatched'
  ];

  @override
  void onInit() {
    super.onInit();
    _generateOrderId();
    _initializeMobileNumber();
  }

  @override
  void onClose() {
    customerNameController.dispose();
    mobileNumberController.dispose();
    gstNumberController.dispose();
    emailIdController.dispose();
    businessNameController.dispose();
    businessAddressController.dispose();
    orderIdController.dispose();
    productNameController.dispose();
    priceController.dispose();
    quantityController.dispose();
    searchController.dispose();
    super.onClose();
  }

  // Generate unique order ID
  void _generateOrderId() {
    final now = DateTime.now();
    final orderId = 'ORD-${now.year}-${now.millisecondsSinceEpoch.toString().substring(8)}';
    orderIdController.text = orderId;
  }

  // Initialize mobile number with default country code
  void _initializeMobileNumber() {
    mobileNumberController.text = '${selectedCountry.value.dialCode} ';
  }

  // Handle navigation item selection
  void selectNavItem(int index) {
    selectedNavIndex.value = index;

    switch (index) {
      case 0:
        // Dashboard
        Get.offNamed('/dashboard');
        break;
      case 1:
        // Customers List
        Get.offNamed('/customers-list');
        break;
      case 2:
        // Orders List
        Get.offNamed('/order-list');
        break;
    }
  }

  // Handle payment selection
  void selectPayment(String payment) {
    selectedPayment.value = payment;
  }

  // Handle order status selection
  void selectOrderStatus(String status) {
    selectedOrderStatus.value = status;
  }

  // Handle country selection
  void selectCountry(CountryModel country) {
    selectedCountry.value = country;

    // Update mobile number with new country code if the field is empty or contains old country code
    final currentText = mobileNumberController.text.trim();
    if (currentText.isEmpty || currentText.startsWith('+')) {
      // If field is empty or starts with a country code, replace with new country code
      mobileNumberController.text = '${country.dialCode} ';
    }
  }

  // Handle product image upload
  void onUploadImageTap() {
    // TODO: Implement image picker functionality
    Get.snackbar(
      'Image Upload',
      'Image upload functionality will be implemented soon',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Handle form submission
  void onAddOrderTap() {
    if (_validateForm()) {
      isLoading.value = true;

      final orderData = AddOrderModel(
        customerName: customerNameController.text,
        mobileNumber: mobileNumberController.text,
        gstNumber: gstNumberController.text,
        emailId: emailIdController.text,
        businessName: businessNameController.text,
        businessAddress: businessAddressController.text,
        orderId: orderIdController.text,
        productName: productNameController.text,
        price: double.tryParse(priceController.text) ?? 0.0,
        quantity: int.tryParse(quantityController.text) ?? 0,
        payment: selectedPayment.value,
        orderStatus: selectedOrderStatus.value,
        productImagePath: productImagePath.value.isEmpty ? null : productImagePath.value,
      );

      // TODO: Save order data to backend/database
      print('Order Data: ${orderData.toJson()}'); // For debugging

      // Simulate API call
      Future.delayed(const Duration(seconds: 2), () {
        isLoading.value = false;
        Get.snackbar(
          'Success',
          'Order ${orderData.orderId} added successfully!',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // Navigate back to order list
        Get.offNamed('/order-list');
      });
    }
  }

  // Form validation
  bool _validateForm() {
    if (customerNameController.text.isEmpty) {
      _showError('Please enter customer name');
      return false;
    }
    if (mobileNumberController.text.isEmpty) {
      _showError('Please enter mobile number');
      return false;
    }
    if (orderIdController.text.isEmpty) {
      _showError('Please enter order ID');
      return false;
    }
    if (productNameController.text.isEmpty) {
      _showError('Please enter product name');
      return false;
    }
    if (priceController.text.isEmpty || double.tryParse(priceController.text) == null) {
      _showError('Please enter valid price');
      return false;
    }
    if (quantityController.text.isEmpty || int.tryParse(quantityController.text) == null) {
      _showError('Please enter valid quantity');
      return false;
    }
    return true;
  }

  // Show error message
  void _showError(String message) {
    Get.snackbar(
      'Error',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }

  // Handle search
  void onSearchChanged(String query) {
    // TODO: Implement search functionality if needed
  }
}
