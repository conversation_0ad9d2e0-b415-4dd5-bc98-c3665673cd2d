class AddCustomerModel {
  final String fullName;
  final String mobileNumber;
  final String emailId;
  final String gstNumber;
  final String businessName;
  final String businessAddress;

  AddCustomerModel({
    required this.fullName,
    required this.mobileNumber,
    required this.emailId,
    required this.gstNumber,
    required this.businessName,
    required this.businessAddress,
  });

  factory AddCustomerModel.fromJson(Map<String, dynamic> json) {
    return AddCustomerModel(
      fullName: json['fullName'] ?? '',
      mobileNumber: json['mobileNumber'] ?? '',
      emailId: json['emailId'] ?? '',
      gstNumber: json['gstNumber'] ?? '',
      businessName: json['businessName'] ?? '',
      businessAddress: json['businessAddress'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'mobileNumber': mobileNumber,
      'emailId': emailId,
      'gstNumber': gstNumber,
      'businessName': businessName,
      'businessAddress': businessAddress,
    };
  }

  AddCustomerModel copyWith({
    String? fullName,
    String? mobileNumber,
    String? emailId,
    String? gstNumber,
    String? businessName,
    String? businessAddress,
  }) {
    return AddCustomerModel(
      fullName: fullName ?? this.fullName,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      emailId: emailId ?? this.emailId,
      gstNumber: gstNumber ?? this.gstNumber,
      businessName: businessName ?? this.businessName,
      businessAddress: businessAddress ?? this.businessAddress,
    );
  }
}
