class OrderModel {
  final String orderId;
  final String customerName;
  final String customerPhone;
  final DateTime orderDate;
  final DateTime deliveryDate;
  final int itemCount;
  final double amount;
  final String status;

  OrderModel({
    required this.orderId,
    required this.customerName,
    required this.customerPhone,
    required this.orderDate,
    required this.deliveryDate,
    required this.itemCount,
    required this.amount,
    required this.status,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      orderId: json['orderId'] ?? '',
      customerName: json['customerName'] ?? '',
      customerPhone: json['customerPhone'] ?? '',
      orderDate: DateTime.parse(json['orderDate']),
      deliveryDate: DateTime.parse(json['deliveryDate']),
      itemCount: json['itemCount'] ?? 0,
      amount: (json['amount'] ?? 0).toDouble(),
      status: json['status'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'orderId': orderId,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'orderDate': orderDate.toIso8601String(),
      'deliveryDate': deliveryDate.toIso8601String(),
      'itemCount': itemCount,
      'amount': amount,
      'status': status,
    };
  }
}
